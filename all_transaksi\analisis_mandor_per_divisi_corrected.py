#!/usr/bin/env python3
"""
Sistem Analisis MANDOR Per Divisi - Versi Diperbaiki
Definisi Role yang Benar:
- P1 = MANDOR
- P5 = ASISTEN  
- PM = KERANI
"""

import os
import sys
import pandas as pd
from datetime import datetime
from collections import defaultdict
import traceback

# Add the all_transaksi directory to Python path
sys.path.append(os.path.dirname(__file__))

from firebird_connector import FirebirdConnector

# Konfigurasi database
DB_PATH = "C:\\Users\\<USER>\\Downloads\\PTRJ_P1A\\PTRJ_P1A.FDB"

def get_employee_role_corrected(recordtag):
    """
    Menentukan role karyawan berdasarkan RECORDTAG yang benar.
    
    Args:
        recordtag: Field RECORDTAG dari transaksi
    
    Returns:
        str: Role karyawan
    """
    if not recordtag:
        return 'LAINNYA'
    
    recordtag_str = str(recordtag).strip().upper()
    
    # Mapping berdasarkan RECORDTAG yang BENAR
    role_mapping = {
        'PM': 'KERANI',    # Plantation Manager/Kerani (tetap sama)
        'P1': 'MANDOR',    # MANDOR (diperbaiki dari sebelumnya P5)
        'P5': 'ASISTEN',   # ASISTEN (diperbaiki dari sebelumnya P1)
    }
    
    return role_mapping.get(recordtag_str, 'LAINNYA')

def get_employee_mapping(connector):
    """
    Mendapatkan mapping antara ID dan NAME dari tabel EMP.
    """
    print("Mendapatkan data mapping ID ke NAME dari tabel EMP...")
    query = """
    SELECT ID, NAME
    FROM EMP
    """

    try:
        result = connector.execute_query(query)
        df = connector.to_pandas(result)
        
        employee_mapping = {}
        
        if not df.empty:
            print(f"Berhasil mendapatkan {len(df)} data karyawan dari database.")
            
            # Cari kolom ID dan NAME
            id_col = None
            name_col = None
            
            for col in df.columns:
                if 'ID' in col.upper() and id_col is None:
                    id_col = col
                if 'NAME' in col.upper() and name_col is None:
                    name_col = col
            
            if id_col and name_col:
                for _, row in df.iterrows():
                    emp_id = str(row[id_col]).strip()
                    emp_name = str(row[name_col]).strip()
                    if emp_id and emp_name:
                        employee_mapping[emp_id] = emp_name
                        
                print(f"Berhasil membuat mapping untuk {len(employee_mapping)} karyawan.")
        else:
            print("Tidak dapat mendapatkan data EMP dari database.")
    except Exception as e:
        print(f"Error saat mengambil data EMP: {e}")
    
    def get_employee_name(emp_id):
        emp_id_str = str(emp_id).strip()
        if emp_id_str in employee_mapping:
            return employee_mapping[emp_id_str]
        return f"KARYAWAN-{emp_id_str}"
    
    employee_mapping['get_name'] = get_employee_name
    print(f"Total mapping karyawan: {len(employee_mapping)-1}")
    return employee_mapping

def get_division_list(connector):
    """
    Mendapatkan daftar divisi dari database.
    """
    print("Mendapatkan daftar divisi...")
    query = """
    SELECT DISTINCT b.DIVID, c.DIVNAME, c.DIVCODE
    FROM OCFIELD b
    LEFT JOIN CRDIVISION c ON b.DIVID = c.ID
    WHERE b.DIVID IS NOT NULL
    ORDER BY c.DIVNAME
    """
    
    try:
        result = connector.execute_query(query)
        df = connector.to_pandas(result)
        
        divisions = []
        if not df.empty:
            for _, row in df.iterrows():
                div_id = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ''
                div_name = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ''
                div_code = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ''
                
                if div_id and div_name:
                    divisions.append({
                        'div_id': div_id,
                        'div_name': div_name,
                        'div_code': div_code
                    })
            
            print(f"Ditemukan {len(divisions)} divisi")
        
        return divisions
    except Exception as e:
        print(f"Error mengambil daftar divisi: {e}")
        return []

def analyze_division_transactions(connector, employee_mapping, div_id, div_name, month=5, year=2025):
    """
    Menganalisis transaksi per divisi dengan role yang benar.
    """
    print(f"\nMenganalisis divisi: {div_name} (ID: {div_id})")
    
    # Query untuk mendapatkan semua transaksi di divisi ini
    query = f"""
    SELECT 
        a.ID, 
        a.SCANUSERID, 
        a.OCID, 
        a.WORKERID, 
        a.CARRIERID, 
        a.FIELDID, 
        a.TASKNO, 
        a.RIPEBCH, 
        a.UNRIPEBCH, 
        a.BLACKBCH, 
        a.ROTTENBCH, 
        a.LONGSTALKBCH, 
        a.RATDMGBCH, 
        a.LOOSEFRUIT, 
        a.TRANSNO, 
        a.TRANSDATE, 
        a.TRANSTIME, 
        a.UPLOADDATETIME, 
        a.RECORDTAG, 
        a.TRANSSTATUS, 
        a.TRANSTYPE, 
        a.LASTUSER, 
        a.LASTUPDATED, 
        a.UNDERRIPEBCH, 
        a.OVERRIPEBCH, 
        a.ABNORMALBCH, 
        a.LOOSEFRUIT2,
        b.DIVID AS DIVISI_ID,
        b.FIELDNO AS FIELD_NO
    FROM 
        FFBSCANNERDATA{month:02d} a
    JOIN 
        OCFIELD b ON a.FIELDID = b.ID
    WHERE 
        b.DIVID = '{div_id}'
        AND a.TRANSDATE >= '{year}-{month:02d}-01'
        AND a.TRANSDATE <= '{year}-{month:02d}-29'
    ORDER BY a.SCANUSERID, a.TRANSDATE, a.TRANSTIME
    """
    
    try:
        result = connector.execute_query(query)
        df = connector.to_pandas(result)
        
        if df.empty:
            print(f"  Tidak ada transaksi di divisi {div_name}")
            return None
        
        print(f"  Ditemukan {len(df)} transaksi")
        
        # Analisis per role
        role_stats = defaultdict(lambda: defaultdict(lambda: {
            'employee_id': '',
            'employee_name': '',
            'role': '',
            'total_transactions': 0,
            'verified_transactions': 0,
            'transaction_details': []
        }))
        
        # Column mapping
        scanuserid_col = 1   # SCANUSERID
        recordtag_col = 18   # RECORDTAG  
        transstatus_col = 19 # TRANSSTATUS
        transno_col = 14     # TRANSNO
        transdate_col = 15   # TRANSDATE
        
        for _, row in df.iterrows():
            try:
                scanner_user_id = str(row.iloc[scanuserid_col]).strip() if pd.notna(row.iloc[scanuserid_col]) else ''
                recordtag = str(row.iloc[recordtag_col]).strip() if pd.notna(row.iloc[recordtag_col]) else ''
                transstatus = str(row.iloc[transstatus_col]).strip() if pd.notna(row.iloc[transstatus_col]) else ''
                transno = str(row.iloc[transno_col]).strip() if pd.notna(row.iloc[transno_col]) else ''
                transdate = str(row.iloc[transdate_col]).strip() if pd.notna(row.iloc[transdate_col]) else ''
                
                if scanner_user_id and recordtag:
                    # Determine role dengan mapping yang benar
                    role = get_employee_role_corrected(recordtag)
                    
                    # Get employee name
                    if 'get_name' in employee_mapping:
                        employee_name = employee_mapping['get_name'](scanner_user_id)
                    else:
                        employee_name = employee_mapping.get(scanner_user_id, f"EMPLOYEE-{scanner_user_id}")
                    
                    # Update statistics
                    role_stats[role][scanner_user_id]['employee_id'] = scanner_user_id
                    role_stats[role][scanner_user_id]['employee_name'] = employee_name
                    role_stats[role][scanner_user_id]['role'] = role
                    role_stats[role][scanner_user_id]['total_transactions'] += 1
                    
                    # Count verifications (status 704)
                    if transstatus == '704':
                        role_stats[role][scanner_user_id]['verified_transactions'] += 1
                    
                    role_stats[role][scanner_user_id]['transaction_details'].append({
                        'transno': transno,
                        'transdate': transdate,
                        'transstatus': transstatus,
                        'recordtag': recordtag
                    })
                    
            except Exception as e:
                continue
        
        return {
            'div_id': div_id,
            'div_name': div_name,
            'total_transactions': len(df),
            'role_stats': role_stats
        }
        
    except Exception as e:
        print(f"  Error menganalisis divisi {div_name}: {e}")
        return None

def generate_division_report(division_results, output_dir, month, year):
    """
    Membuat laporan per divisi dalam format Excel seperti yang diminta.
    """
    print("\nMembuat laporan per divisi...")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"laporan_mandor_per_divisi_{month:02d}_{year}_{timestamp}.xlsx"
    filepath = os.path.join(output_dir, filename)
    
    with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
        
        # Sheet 1: Summary All Divisions
        summary_data = []
        
        for div_result in division_results:
            if div_result is None:
                continue
                
            div_name = div_result['div_name']
            role_stats = div_result['role_stats']
            
            # Calculate totals per role
            kerani_total = sum(emp['total_transactions'] for emp in role_stats.get('KERANI', {}).values())
            mandor_total = sum(emp['total_transactions'] for emp in role_stats.get('MANDOR', {}).values())
            asisten_total = sum(emp['total_transactions'] for emp in role_stats.get('ASISTEN', {}).values())
            
            kerani_verified = sum(emp['verified_transactions'] for emp in role_stats.get('KERANI', {}).values())
            mandor_verified = sum(emp['verified_transactions'] for emp in role_stats.get('MANDOR', {}).values())
            asisten_verified = sum(emp['verified_transactions'] for emp in role_stats.get('ASISTEN', {}).values())
            
            total_receipts = kerani_total + mandor_total + asisten_total
            
            # Calculate verification percentages
            mandor_verification_pct = (mandor_verified / total_receipts * 100) if total_receipts > 0 else 0
            asisten_verification_pct = (asisten_verified / total_receipts * 100) if total_receipts > 0 else 0
            
            summary_data.append({
                'Division': div_name,
                'Total_Receipts': total_receipts,
                'KERANI_Transactions': kerani_total,
                'MANDOR_Transactions': mandor_total,
                'ASISTEN_Transactions': asisten_total,
                'MANDOR_Verification_Rate': f"{mandor_verification_pct:.2f}%",
                'ASISTEN_Verification_Rate': f"{asisten_verification_pct:.2f}%"
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary All Divisions', index=False)
        
        # Sheet per divisi (seperti format yang diminta)
        for div_result in division_results:
            if div_result is None:
                continue
                
            div_name = div_result['div_name']
            role_stats = div_result['role_stats']
            
            # Buat data seperti format yang diminta
            division_data = []
            
            # KERANI data
            for emp_id, emp_data in role_stats.get('KERANI', {}).items():
                division_data.append({
                    'Division': div_name,
                    'Scanner_User': emp_data['employee_name'],
                    'Scanner_User_ID': emp_id,
                    'Role': 'KERANI',
                    'Conductor': 0,  # KERANI tidak melakukan conduct
                    'Assistant': 0,  # KERANI tidak melakukan assist
                    'Manager': 0,    # KERANI tidak melakukan manage
                    'Bunch_Counter': emp_data['total_transactions']  # KERANI membuat transaksi
                })
            
            # MANDOR data
            for emp_id, emp_data in role_stats.get('MANDOR', {}).items():
                division_data.append({
                    'Division': div_name,
                    'Scanner_User': emp_data['employee_name'],
                    'Scanner_User_ID': emp_id,
                    'Role': 'MANDOR',
                    'Conductor': emp_data['verified_transactions'],  # MANDOR melakukan conduct/verify
                    'Assistant': 0,
                    'Manager': 0,
                    'Bunch_Counter': 0
                })
            
            # ASISTEN data
            for emp_id, emp_data in role_stats.get('ASISTEN', {}).items():
                division_data.append({
                    'Division': div_name,
                    'Scanner_User': emp_data['employee_name'],
                    'Scanner_User_ID': emp_id,
                    'Role': 'ASISTEN',
                    'Conductor': 0,
                    'Assistant': emp_data['verified_transactions'],  # ASISTEN melakukan assist/verify
                    'Manager': 0,
                    'Bunch_Counter': 0
                })
            
            if division_data:
                division_df = pd.DataFrame(division_data)
                
                # Hitung total dan verification rates
                total_receipts = sum(emp['total_transactions'] for role_data in role_stats.values() for emp in role_data.values())
                mandor_verified = sum(emp['verified_transactions'] for emp in role_stats.get('MANDOR', {}).values())
                asisten_verified = sum(emp['verified_transactions'] for emp in role_stats.get('ASISTEN', {}).values())
                
                mandor_verification_pct = (mandor_verified / total_receipts * 100) if total_receipts > 0 else 0
                asisten_verification_pct = (asisten_verified / total_receipts * 100) if total_receipts > 0 else 0
                
                # Add summary rows
                summary_row = pd.DataFrame([{
                    'Division': '',
                    'Scanner_User': f'Total Receipt: {total_receipts}',
                    'Scanner_User_ID': '',
                    'Role': '',
                    'Conductor': '',
                    'Assistant': '',
                    'Manager': '',
                    'Bunch_Counter': ''
                }])
                
                verification_row = pd.DataFrame([{
                    'Division': '',
                    'Scanner_User': f'MANDOR Verification: {mandor_verification_pct:.2f}%',
                    'Scanner_User_ID': f'ASISTEN Verification: {asisten_verification_pct:.2f}%',
                    'Role': '',
                    'Conductor': '',
                    'Assistant': '',
                    'Manager': '',
                    'Bunch_Counter': ''
                }])
                
                # Combine all data
                final_df = pd.concat([division_df, summary_row, verification_row], ignore_index=True)
                
                # Clean sheet name
                sheet_name = div_name.replace('/', '_').replace('\\', '_')[:31]
                final_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # Sheet 3: Detail Employee Mapping
        mapping_data = []
        for div_result in division_results:
            if div_result is None:
                continue
                
            div_name = div_result['div_name']
            role_stats = div_result['role_stats']
            
            for role, employees in role_stats.items():
                for emp_id, emp_data in employees.items():
                    mapping_data.append({
                        'Division': div_name,
                        'Employee_ID': emp_id,
                        'Employee_Name': emp_data['employee_name'],
                        'Role': role,
                        'Total_Transactions': emp_data['total_transactions'],
                        'Verified_Transactions': emp_data['verified_transactions'],
                        'Verification_Rate': f"{(emp_data['verified_transactions']/emp_data['total_transactions']*100):.2f}%" if emp_data['total_transactions'] > 0 else "0%"
                    })
        
        mapping_df = pd.DataFrame(mapping_data)
        mapping_df.to_excel(writer, sheet_name='Employee Mapping', index=False)
    
    print(f"Laporan disimpan: {filepath}")
    return filepath

def main():
    """
    Fungsi utama untuk menjalankan analisis MANDOR per divisi yang diperbaiki.
    """
    try:
        print("ANALISIS MANDOR PER DIVISI - VERSI DIPERBAIKI")
        print("="*60)
        print("Role Definition:")
        print("- P1 = MANDOR")
        print("- P5 = ASISTEN") 
        print("- PM = KERANI")
        print("="*60)
        
        # Setup database connection
        print("Menghubungkan ke database...")
        connector = FirebirdConnector(DB_PATH)
        
        if not connector.test_connection():
            print("Koneksi database gagal!")
            return
        
        print("Koneksi database berhasil")
        
        # Load employee mapping
        employee_mapping = get_employee_mapping(connector)
        
        # Get division list
        divisions = get_division_list(connector)
        
        if not divisions:
            print("Tidak ada divisi ditemukan!")
            return
        
        # Set period untuk analisis (April 2025)
        year = 2025
        month = 4
        
        print(f"\nPeriode analisis: {month:02d}/{year}")
        print(f"Menganalisis {len(divisions)} divisi...")
        
        # Analyze each division
        division_results = []
        
        for division in divisions:
            div_id = division['div_id']
            div_name = division['div_name']
            
            result = analyze_division_transactions(
                connector, employee_mapping, div_id, div_name, month, year)
            
            if result:
                division_results.append(result)
                
                # Print summary for this division
                role_stats = result['role_stats']
                print(f"  Divisi {div_name}:")
                print(f"    - KERANI: {len(role_stats.get('KERANI', {}))} orang")
                print(f"    - MANDOR: {len(role_stats.get('MANDOR', {}))} orang")
                print(f"    - ASISTEN: {len(role_stats.get('ASISTEN', {}))} orang")
                print(f"    - Total transaksi: {result['total_transactions']}")
        
        if not division_results:
            print("Tidak ada data transaksi ditemukan!")
            return
        
        # Generate report
        output_dir = "reports"
        report_path = generate_division_report(division_results, output_dir, month, year)
        
        # Generate console summary
        print("\n" + "="*80)
        print("RINGKASAN ANALISIS MANDOR PER DIVISI")
        print("="*80)
        
        total_divisions = len(division_results)
        total_transactions = sum(result['total_transactions'] for result in division_results)
        
        print(f"Total Divisi Dianalisis: {total_divisions}")
        print(f"Total Transaksi: {total_transactions}")
        
        # Summary per role across all divisions
        all_kerani = set()
        all_mandor = set()
        all_asisten = set()
        
        for result in division_results:
            role_stats = result['role_stats']
            all_kerani.update(role_stats.get('KERANI', {}).keys())
            all_mandor.update(role_stats.get('MANDOR', {}).keys())
            all_asisten.update(role_stats.get('ASISTEN', {}).keys())
        
        print(f"\nTotal Unique Employees:")
        print(f"- KERANI: {len(all_kerani)} orang")
        print(f"- MANDOR: {len(all_mandor)} orang") 
        print(f"- ASISTEN: {len(all_asisten)} orang")
        
        print(f"\nLAPORAN SELESAI!")
        print(f"File disimpan di: {report_path}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 