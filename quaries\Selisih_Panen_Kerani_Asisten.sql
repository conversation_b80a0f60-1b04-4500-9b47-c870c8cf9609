SELECT a.ID, a.SCANUSERID, a.OCID, a.WORKERI<PERSON>, a.CARRIER<PERSON>, a.FIELD<PERSON>, a.TASK<PERSON><PERSON>, 
       a.RIPE<PERSON><PERSON>, a.UNRIPEBCH, a.BLACKBCH, a.ROTTENBCH, a.<PERSON><PERSON><PERSON>, a.<PERSON>TDM<PERSON><PERSON>, 
       a.LOOSEFRUIT, a.TRANS<PERSON>, a.TRANSDATE, a.TRANSTI<PERSON>, a.UPLOADDATETIME, 
       a.RECORDTAG, a.TRANSSTATUS, a.TRANSTYPE, a.LASTUSER, a.LASTUPDATED, 
       a.OVERRIPEBCH, a.<PERSON>DERRIPEBCH, a.ABNORMALBCH, a.LOOSEFRUIT2
FROM FFBSCANNERDATA04 a
WHERE a.TRANSDATE >= '2025-03-01' AND a.TRANSDATE < '2025-04-01'
AND a.TRANSNO IN (
    SELECT TRANSNO
    FROM FFBSCANNERDATA04
    WHERE TRANSDATE >= '2025-03-01' AND TRANSDATE < '2025-04-01'
    GROUP BY TRANSNO
    HAVING COUNT(*) > 1
)
ORDER BY a.TRANSNO, a.TRANSDATE;