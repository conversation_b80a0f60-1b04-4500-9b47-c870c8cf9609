#!/usr/bin/env python3
"""
Correct Analysis Engine untuk FFB Scanner
Menggunakan logika yang sudah diperbaiki dari create_correct_report.py
"""

import pandas as pd
from collections import defaultdict
from firebird_connector import FirebirdConnector

def get_employee_names(connector):
    """Get employee names mapping"""
    query = "SELECT ID, NAME FROM EMP"
    try:
        result = connector.execute_query(query)
        df = connector.to_pandas(result)
        
        names = {}
        if not df.empty:
            for _, row in df.iterrows():
                emp_id = str(row.iloc[0]).strip()
                emp_name = str(row.iloc[1]).strip()
                names[emp_id] = emp_name
        return names
    except Exception as e:
        print(f"Error loading employee names: {e}")
        return {}

def analyze_division_correct(connector, emp_names, div_id, div_name, start_date, end_date):
    """
    Analyze division dengan logika yang benar
    Menggunakan query langsung tanpa filter status
    """
    print(f"Analyzing {div_name} (ID: {div_id}) from {start_date} to {end_date}")
    
    # Format dates
    start_str = start_date.strftime('%Y-%m-%d') if hasattr(start_date, 'strftime') else str(start_date)
    end_str = end_date.strftime('%Y-%m-%d') if hasattr(end_date, 'strftime') else str(end_date)
    
    # Determine table based on month
    month = start_date.month if hasattr(start_date, 'month') else int(str(start_date).split('-')[1])
    table_name = f"FFBSCANNERDATA{month:02d}"
    
    # Query untuk menghitung transaksi per user per recordtag (TANPA filter status)
    query = f"""
    SELECT 
        a.SCANUSERID,
        a.RECORDTAG,
        COUNT(*) as count
    FROM 
        {table_name} a
    JOIN 
        OCFIELD b ON a.FIELDID = b.ID
    WHERE 
        b.DIVID = '{div_id}'
        AND a.TRANSDATE >= '{start_str}' 
        AND a.TRANSDATE < '{end_str}'
    GROUP BY a.SCANUSERID, a.RECORDTAG
    ORDER BY a.SCANUSERID, a.RECORDTAG
    """
    
    try:
        result = connector.execute_query(query)
        df = connector.to_pandas(result)
        
        if df.empty:
            print(f"  No data found for {div_name}")
            return None
        
        # Process data
        employees = {}
        totals = {'PM': 0, 'P1': 0, 'P5': 0}
        
        for _, row in df.iterrows():
            user_id = str(row.iloc[0]).strip()
            recordtag = str(row.iloc[1]).strip()
            count = int(row.iloc[2])
            
            if user_id not in employees:
                employees[user_id] = {
                    'name': emp_names.get(user_id, f"EMPLOYEE-{user_id}"),
                    'PM': 0, 'P1': 0, 'P5': 0
                }
            
            employees[user_id][recordtag] = count
            totals[recordtag] += count
        
        # Calculate verification statistics
        total_kerani = totals['PM']
        total_mandor = totals['P1']
        total_asisten = totals['P5']
        total_verifications = total_mandor + total_asisten
        verification_rate = (total_verifications / total_kerani * 100) if total_kerani > 0 else 0
        
        # Calculate individual contributions
        for user_id, emp_data in employees.items():
            # Contribution to total kerani transactions
            pm_contribution = (emp_data['PM'] / total_kerani * 100) if total_kerani > 0 else 0
            p1_contribution = (emp_data['P1'] / total_kerani * 100) if total_kerani > 0 else 0
            p5_contribution = (emp_data['P5'] / total_kerani * 100) if total_kerani > 0 else 0
            
            emp_data['pm_contribution'] = pm_contribution
            emp_data['p1_contribution'] = p1_contribution
            emp_data['p5_contribution'] = p5_contribution
        
        print(f"  KERANI (PM): {total_kerani}")
        print(f"  MANDOR (P1): {total_mandor}")
        print(f"  ASISTEN (P5): {total_asisten}")
        print(f"  Verification Rate: {verification_rate:.2f}%")
        
        # Special check for Erly in Air Kundo
        if div_id == '16' and '4771' in employees:
            erly_pm = employees['4771']['PM']
            print(f"  ⭐ Erly (4771): {erly_pm} PM transactions")
            print(f"     Expected: 123 - {'✅ MATCH' if erly_pm == 123 else '❌ MISMATCH'}")
        
        return {
            'div_id': div_id,
            'div_name': div_name,
            'employees': employees,
            'totals': totals,
            'verification_stats': {
                'total_kerani_transactions': total_kerani,
                'total_mandor_transactions': total_mandor,
                'total_asisten_transactions': total_asisten,
                'total_verifications': total_verifications,
                'verification_rate': verification_rate
            },
            'start_date': start_str,
            'end_date': end_str
        }
        
    except Exception as e:
        print(f"  Error analyzing {div_name}: {e}")
        return None

def analyze_multiple_divisions(connector, divisions, start_date, end_date):
    """Analyze multiple divisions"""
    print(f"\nAnalyzing {len(divisions)} divisions from {start_date} to {end_date}")
    print("="*60)
    
    # Get employee names
    emp_names = get_employee_names(connector)
    print(f"Loaded {len(emp_names)} employee names")
    
    results = []
    
    for division in divisions:
        div_id = division.get('div_id') or division.get('id')
        div_name = division.get('div_name') or division.get('name')
        
        if not div_id or not div_name:
            print(f"Skipping invalid division: {division}")
            continue
        
        result = analyze_division_correct(connector, emp_names, div_id, div_name, start_date, end_date)
        if result:
            results.append(result)
    
    return results

def get_divisions_list(connector):
    """Get list of divisions"""
    query = """
    SELECT DISTINCT 
        d.ID as div_id,
        d.NAME as div_name
    FROM DIVISION d
    ORDER BY d.NAME
    """
    
    try:
        result = connector.execute_query(query)
        df = connector.to_pandas(result)
        
        divisions = []
        if not df.empty:
            for _, row in df.iterrows():
                div_id = str(row.iloc[0]).strip()
                div_name = str(row.iloc[1]).strip()
                divisions.append({
                    'div_id': div_id,
                    'div_name': div_name
                })
        
        return divisions
    except Exception as e:
        print(f"Error loading divisions: {e}")
        # Return default divisions if query fails
        return [
            {'div_id': '15', 'div_name': 'Air Batu'},
            {'div_id': '16', 'div_name': 'Air Kundo'},
            {'div_id': '17', 'div_name': 'Air Hijau'}
        ]

def test_correct_analysis():
    """Test the correct analysis"""
    from datetime import datetime
    
    DB_PATH = "C:\\Users\\<USER>\\Downloads\\PTRJ_P1A\\PTRJ_P1A.FDB"
    
    print("TESTING CORRECT ANALYSIS ENGINE")
    print("="*50)
    
    try:
        connector = FirebirdConnector(DB_PATH)
        
        if not connector.test_connection():
            print("❌ Database connection failed!")
            return
        
        print("✅ Database connected")
        
        # Test Air Kundo
        start_date = datetime(2025, 4, 1)
        end_date = datetime(2025, 4, 29)  # Use < 2025-04-29 in query
        
        divisions = [{'div_id': '16', 'div_name': 'Air Kundo'}]
        
        results = analyze_multiple_divisions(connector, divisions, start_date, end_date)
        
        if results:
            result = results[0]
            employees = result['employees']
            
            if '4771' in employees:
                erly_pm = employees['4771']['PM']
                print(f"\n🎯 VERIFICATION:")
                print(f"Erly (4771) PM transactions: {erly_pm}")
                print(f"Expected: 123")
                print(f"Status: {'✅ CORRECT' if erly_pm == 123 else '❌ INCORRECT'}")
            else:
                print("❌ Erly not found in results")
        else:
            print("❌ No results returned")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_correct_analysis()
